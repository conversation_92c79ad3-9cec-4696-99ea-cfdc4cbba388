{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "53e0716ece92b92069470f6253c358c8", "packages": [{"name": "superkoders/custom-fields", "version": "dev-main", "source": {"type": "git", "url": "https://gitlab.superkoders.com/packages/custom-fields", "reference": "719ea2c3b377ba2357eae02b418773ac87208adf"}, "require": {"php": ">=8.2"}, "default-branch": true, "type": "library", "autoload": {"psr-4": {"App\\Model\\CustomField\\": "src/"}}, "license": ["MIT"], "description": "CustomFields DI extension for SA", "time": "2025-09-09T12:18:13+00:00"}, {"name": "superkoders/messenger", "version": "dev-main", "source": {"type": "git", "url": "https://gitlab.superkoders.com/packages/messenger", "reference": "b7ab03007427af963958711e568c1d3d690aeb18"}, "require": {"php": ">=8.2"}, "default-branch": true, "type": "library", "autoload": {"classmap": ["src/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.jakubmikita.sk"}], "description": "Integration of symfony/messenger to SA", "time": "2025-09-09T12:03:12+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "dev", "stability-flags": {"superkoders/custom-fields": 20, "superkoders/messenger": 20}, "prefer-stable": true, "prefer-lowest": false, "platform": {}, "platform-dev": {}, "platform-overrides": {"php": "8.4.0"}, "plugin-api-version": "2.6.0"}